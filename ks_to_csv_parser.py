import os
import csv
import re
from config import INPUT_FOLDER, WORK_FOLDER

def extract_tags(text):
    """Extract tags like [r], [p][r] from text and return (clean_text, tags)"""
    # Find all tags in the format [something]
    tags = re.findall(r'\[.*?\]', text)
    # Remove all tags from text
    clean_text = re.sub(r'\[.*?\]', '', text)
    return clean_text.strip(), ''.join(tags)

def is_sentence_complete(text):
    """Check if a sentence is complete based on Japanese punctuation"""
    return any(text.rstrip().endswith(char) 
              for char in ['。', '！', '？', '」', '.', '!', '?'])

def is_sentence_continuation(text):
    """Check if a sentence continues based on Japanese grammar"""
    text = text.rstrip()
    continuation_markers = ['、', ',', 'て', 'で', 'が', 'けど', 'から', 'ので', 'のに']
    return any(text.endswith(marker) for marker in continuation_markers)

def process_ks_file(input_file_path):
    # Create work folder if it doesn't exist
    if not os.path.exists(WORK_FOLDER):
        os.makedirs(WORK_FOLDER)
    
    # Get the filename from the path
    filename = os.path.basename(input_file_path)
    ks_output_path = os.path.join(WORK_FOLDER, filename)
    csv_output_path = os.path.join(WORK_FOLDER, filename.replace('.ks', '.csv'))
    
    with open(input_file_path, 'r', encoding='utf-8') as infile:
        lines = infile.readlines()
    
    # Process lines and add IDs
    processed_lines = []
    csv_rows = []
    current_id = 1
    current_speaker = "narrator"  # Default speaker
    sentence_group_id = 1
    in_dialogue = False  # Track if we're inside a dialogue block

    for line in lines:
        line = line.rstrip()
        if line.strip():  # If line is not empty
            # Add ID as a comment at the end of the line
            processed_lines.append(f"{line} ;ID:{current_id}")
            
            # Check for character name in brackets
            if line.startswith('[') and line.endswith(']') and not any(cmd in line for cmd in ['file', 'type', '人物', '音楽', '効果音', '会話シーン', 'イベント絵表示', 'voice', 'cm', 'pcm', 'フラッシュ', '揺れ', '人物消去']):
                current_speaker = line[1:-1]  # Remove brackets
            elif line.startswith('[人物消去]'):
                current_speaker = "narrator"  # Reset speaker to narrator
                in_dialogue = False  # Reset dialogue state
            
            # Extract dialogue text (lines that don't start with [ and contain Japanese text)
            elif not line.startswith('[') and not line.startswith(';'):
                # Remove [pcm], [r], and other tags
                clean_text, tags = extract_tags(line.strip())
                clean_text = clean_text.replace('[pcm]', '').replace('[r]', '').strip()
                
                if clean_text:  # Only process if there's actual text content
                    # Check for Japanese characters to filter out command lines
                    if any('\u3040' <= char <= '\u309F' or '\u30A0' <= char <= '\u30FF' or '\u4E00' <= char <= '\u9FAF' for char in clean_text):
                        # Track dialogue state
                        if '「' in clean_text and not in_dialogue:
                            in_dialogue = True
                        
                        # Determine if this is dialogue
                        is_dialogue = in_dialogue or (current_speaker != "narrator" and ('「' in clean_text or '」' in clean_text))
                        
                        # Check if dialogue ends
                        if '」' in clean_text and in_dialogue:
                            in_dialogue = False

                        actual_speaker = current_speaker if is_dialogue else "narrator"
                        
                        # Determine sentence completion status
                        if actual_speaker != 'narrator':
                            sentence_status = "complete"
                            is_complete = True
                        else:
                            is_complete = is_sentence_complete(clean_text)
                            is_continuing = is_sentence_continuation(clean_text)
                            sentence_status = "complete" if is_complete else (
                                "continuation" if is_continuing else "incomplete")
                        
                        csv_rows.append({
                            'row_id': current_id,
                            'speaker': actual_speaker,
                            'japanese_text': clean_text,
                            'english_translation': '',
                            'tags': tags,
                            'context_before': '',
                            'context_after': '',
                            'sentence_status': sentence_status,
                            'sentence_group_id': sentence_group_id
                        })
                        
                        # Increment sentence group ID only when sentence is complete
                        if is_complete:
                            sentence_group_id += 1
            
            current_id += 1
        else:
            processed_lines.append(line)
    
    # Add context for each row
    for i, row in enumerate(csv_rows):
        # Look back for context
        j = i - 1
        context_before = []
        while j >= 0 and csv_rows[j]['speaker'] == row['speaker']:
            if not any(csv_rows[j]['japanese_text'].rstrip().endswith(char) 
                      for char in ['。', '！', '？', '」']):
                context_before.insert(0, csv_rows[j]['japanese_text'])
            else:
                break
            j -= 1
        
        # Look ahead for context
        j = i + 1
        context_after = []
        while j < len(csv_rows) and csv_rows[j]['speaker'] == row['speaker']:
            context_after.append(csv_rows[j]['japanese_text'])
            if any(csv_rows[j]['japanese_text'].rstrip().endswith(char) 
                  for char in ['。', '！', '？', '」']):
                break
            j += 1
        
        row['context_before'] = ' '.join(context_before)
        row['context_after'] = ' '.join(context_after)
    
    # Write KS file with IDs
    with open(ks_output_path, 'w', encoding='utf-8') as outfile:
        outfile.write('\n'.join(processed_lines))
    
    # Write CSV file with context
    with open(csv_output_path, 'w', encoding='utf-8', newline='') as csvfile:
        fieldnames = ['row_id', 'speaker', 'japanese_text', 'english_translation', 'tags', 
                     'context_before', 'context_after', 'sentence_status', 'sentence_group_id']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_rows)

    return ks_output_path, csv_output_path

def process_all_ks_files():
    """Process all .ks files in the INPUT_FOLDER"""
    if not os.path.exists(INPUT_FOLDER):
        print(f"Error: Input folder '{INPUT_FOLDER}' does not exist")
        return

    # Get all .ks files in the input folder
    ks_files = [f for f in os.listdir(INPUT_FOLDER) if f.endswith('.ks')]
    
    if not ks_files:
        print(f"No .ks files found in '{INPUT_FOLDER}'")
        return

    processed_files = []
    for ks_file in ks_files:
        input_path = os.path.join(INPUT_FOLDER, ks_file)
        try:
            ks_output, csv_output = process_ks_file(input_path)
            processed_files.append((ks_file, ks_output, csv_output))
            print(f"Processed '{ks_file}':")
            print(f"  KS file saved to: {ks_output}")
            print(f"  CSV file saved to: {csv_output}")
        except Exception as e:
            print(f"Error processing '{ks_file}': {str(e)}")

    print(f"\nProcessed {len(processed_files)} files")
    return processed_files

if __name__ == "__main__":
    process_all_ks_files()

import os
import csv
import json
import sys
import io
from collections import deque
from dotenv import load_dotenv
from openai import OpenAI
from tqdm import tqdm
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type
)

from config import (WORK_FOLDER, CONTEXT_WINDOW_SIZE, 
                   CHARACTER_FILE, OPENAI, MODEL_OPENAI, DEEPSEEK, MODEL_DEEPSEEK)

api_provider = DEEPSEEK
load_dotenv(override=True)


class Translator:
    def __init__(self, temperature=0.5, api_provider=api_provider, max_retries=3):
        """Initialize the translator with API config"""
        # Force UTF-8 output for console
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        
        self.temperature = temperature
        self.api_provider = api_provider.lower()
        self.total_tokens = 0
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.translation_history = deque(maxlen=CONTEXT_WINDOW_SIZE)
        self.max_retries = max_retries
        self.characters = self.load_characters()

        # Initialize the client based on the API provider
        if self.api_provider == 'openai':
            self.client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            self.model = MODEL_OPENAI
        elif self.api_provider == 'deepseek':
            self.client = OpenAI(api_key=os.getenv("DEEPSEEK_API_KEY"), base_url="https://api.deepseek.com")
            self.model = MODEL_DEEPSEEK

    def load_characters(self):
        """Load character information from JSON file"""
        try:
            with open(CHARACTER_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load character file: {e}")
            return {}

    def find_mentioned_characters(self, text):
        """Find characters mentioned in the text"""
        mentioned_chars = set()
        
        # Check for both Japanese names and English names
        for char_id, info in self.characters.items():
            if char_id in text or info['japanese_name'] in text or info['english_name'].lower() in text.lower():
                mentioned_chars.add(char_id)
        
        return mentioned_chars

    def get_character_context(self, speaker, mentioned_chars=None):
        """Get character information for the context"""
        context = "\nCharacter Information:\n"
        
        # Add speaker info if exists
        if speaker and speaker != 'narrator':
            char_info = self.characters.get(speaker, {})
            if char_info:
                context += "Speaker:\n"
                context += f"Japanese Name: {char_info.get('japanese_name', speaker)}\n"
                context += f"English Name: {char_info.get('english_name', speaker)}\n"
                context += f"Gender: {char_info.get('gender', 'unknown')}\n"
                
                description = char_info.get('description', '').strip()
                if description:
                    context += f"Description: {description}\n"
                
                alt_names = [
                    k for k, v in self.characters.items() 
                    if v.get('english_name') == char_info['english_name'] and k != speaker
                ]
                if alt_names:
                    context += f"Alternative Japanese Names: {', '.join(alt_names)}\n"
                context += "\n"
        
        # Add mentioned characters info
        if mentioned_chars:
            context += "Mentioned Characters:\n"
            for char_id in mentioned_chars:
                if char_id != speaker:  # Skip if same as speaker
                    char_info = self.characters.get(char_id, {})
                    context += f"- {char_id}:\n"
                    context += f"  Japanese Name: {char_info.get('japanese_name', char_id)}\n"
                    context += f"  English Name: {char_info.get('english_name', char_id)}\n"
                    context += f"  Gender: {char_info.get('gender', 'unknown')}\n"
                    
                    description = char_info.get('description', '').strip()
                    if description:
                        context += f"  Description: {description}\n"
                    
                    alt_names = [
                        k for k, v in self.characters.items() 
                        if v.get('english_name') == char_info['english_name'] and k != char_id
                    ]
                    if alt_names:
                        context += f"  Alternative Japanese Names: {', '.join(alt_names)}\n"
        
        return context.strip()

    def format_history(self):
        """Format translation history for context"""
        if not self.translation_history:
            return ""
        
        history = "\nPrevious translations for context (DO NOT repeat these):\n"
        for entry in self.translation_history:
            speaker = entry['speaker']
            en_text = entry['translation']
            history += f"Speaker: {speaker}\nTranslation: {en_text}\n---\n"
        return history
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((Exception)),
        reraise=True
    )
    def translate(self, japanese_text, speaker=None, context_before='', context_after=''):
        """Translate the given text using the API, with context"""
        try:
            # Clean the input text
            cleaned_text = self._clean_text(japanese_text)
            
            # Build full context text
            full_text = cleaned_text
            if context_before:
                full_text = context_before + ' ' + full_text
            if context_after:
                full_text = full_text + ' ' + context_after
            
            # Find mentioned characters in the full text
            mentioned_chars = self.find_mentioned_characters(full_text)
            
            context = f"Speaker: {speaker}\nFull context: {full_text}\nTranslate ONLY this specific line: {cleaned_text}" if speaker else f"Translate ONLY this line: {cleaned_text}"
            history_context = self.format_history()
            character_context = self.get_character_context(speaker, mentioned_chars)
            
            messages = [
                {"role": "system", "content": f"""            
                ROLE: You are an expert Japanese-to-English translator specializing in visual novels and games.

                TRANSLATION RULES:
                1. OUTPUT THE TRANSLATION ONLY - no explanations, notes, or additional text
                2. Do not output 'Speaker' in your translation
                3. DO NOT repeat or include any previous translations from the context
                4. Preserve all formatting exactly as in the original:
                - Maintain Japanese quotation marks 「」
                - Keep all spacing and line breaks
                5. Translation style:
                - Maintain the original tone (formal/casual/vulgar)
                - Keep character-specific speech patterns consistent
                - Preserve honorifics (-san, -chan, etc.)
                6. IMPORTANT: Only translate the specified line, not the context
                 Characters information context:
                {character_context}
                Previous translations for context (DO NOT repeat these):
                {history_context}"""},

                {"role": "user", "content": context}
            ]

            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=self.temperature,
            )
            
            # Clean the translation output before returning
            translation = response.choices[0].message.content

            # Add to translation history
            self.translation_history.append({
                'speaker': speaker or 'narrator',
                'translation': translation
            })

            print(f"Translation: {translation}")
            
            self.total_tokens += response.usage.total_tokens
            self.prompt_tokens += response.usage.prompt_tokens
            self.completion_tokens += response.usage.completion_tokens
            return translation

        except Exception as e:
            print(f"Translation error: {str(e)}")
            raise

    def translate_csv(self, csv_path):
        """Translate CSV file row by row with context"""
        with open(csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
            fieldnames = reader.fieldnames + ['sentence_status', 'sentence_group_id']

        # Group rows by sentence_group_id and speaker
        sentence_groups = {}
        for row in rows:
            group_id = row['sentence_group_id']
            speaker = row['speaker']
            group_key = f"{group_id}_{speaker}"  # Create unique key combining group_id and speaker
            if group_key not in sentence_groups:
                sentence_groups[group_key] = []
            sentence_groups[group_key].append(row)

        untranslated_groups = sum(1 for group in sentence_groups.values() 
                                if not any(row['english_translation'] for row in group))
        
        with tqdm(total=untranslated_groups, desc="Translating") as pbar:
            for group_key, group_rows in sentence_groups.items():
                if not any(row['english_translation'] for row in group_rows):
                    try:
                        # Combine all Japanese text in the group
                        combined_text = ' '.join(row['japanese_text'] for row in group_rows)
                        
                        # Get the speaker (should be same for all rows in group)
                        speaker = group_rows[0]['speaker']
                        group_id = group_rows[0]['sentence_group_id']
                        
                        # Get context from surrounding groups with the same speaker
                        context_before = self._get_group_context(sentence_groups, group_id, speaker, -1)
                        context_after = self._get_group_context(sentence_groups, group_id, speaker, 1)
                        
                        # Translate the combined text
                        translation = self.translate(
                            japanese_text=combined_text,
                            speaker=speaker if speaker != 'narrator' else None,
                            context_before=context_before,
                            context_after=context_after
                        )
                        
                        if translation:
                            # For both narrator and dialogue, put full translation in last row and mark others as parts
                            # This preserves dialogue integrity and prevents repetition
                            for row in group_rows[:-1]:
                                row['english_translation'] = f"[PART OF sentence_group_id={group_id}]"
                            group_rows[-1]['english_translation'] = self._clean_translation(translation)
                            
                            # Write updates back to CSV
                            with open(csv_path, 'w', encoding='utf-8', newline='') as csvfile:
                                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                                writer.writeheader()
                                writer.writerows(rows)
                            
                            pbar.update(1)
                            
                    except Exception as e:
                        print(f"\nError translating group {group_key}: {e}")
                        raise

    def _get_group_context(self, sentence_groups, current_group_id, current_speaker, offset):
        """Get context from surrounding groups for the same speaker"""
        target_group_id = str(int(current_group_id) + offset)
        target_key = f"{target_group_id}_{current_speaker}"
        if target_key in sentence_groups:
            return ' '.join(row['japanese_text'] for row in sentence_groups[target_key])
        return ''

    def _clean_text(self, text):
        """Clean the input text before translation"""
        return (text
            .replace('♡', '')
            .replace('\u2661', '')  # heart
            .replace('\u2014', '--')  # em dash
            .replace('\u2015', '--')  # horizontal bar
            .replace('\u2500', '-')   # box drawings light horizontal
            .replace('\u2501', '-')   # box drawings heavy horizontal
            .replace('\u2013', '-')
            .replace('一', '-')
            .replace('—', "-")
            .strip()
        )

    def _clean_translation(self, text):
        """Clean the translation output"""
        return (text
            .strip()
            .replace('\n\n', '\n')    # Remove double line breaks
            .replace('...', '…')      # Replace triple dots with ellipsis
            .replace('--', '—')       # Replace double dash with em dash
            .replace('"', '"')        # Replace straight quotes with curly quotes
            .replace('"', '"')
        )

    def _split_translation(self, translation, num_parts):
        """Split translation into roughly equal parts based on sentence structure"""
        # Remove quotes for processing
        text = translation.strip('"')
        
        # Split on common sentence endings
        parts = []
        current_part = ""
        words = text.split()
        words_per_part = len(words) // num_parts
        
        for i, word in enumerate(words):
            current_part += word + " "
            
            # Check if we should split here
            if (i + 1) % words_per_part == 0 and len(parts) < num_parts - 1:
                # Try to find a natural break point
                if any(current_part.strip().endswith(end) for end in [',', '.', '!', '?', '~']):
                    parts.append(f'"{current_part.strip()}"')
                    current_part = ""
                else:
                    # If no natural break, look ahead for one
                    look_ahead = min(3, len(words) - i - 1)
                    for j in range(look_ahead):
                        if any(words[i + j].endswith(end) for end in [',', '.', '!', '?', '~']):
                            parts.append(f'"{current_part.strip() + " " + " ".join(words[i+1:i+j+1])}"')
                            current_part = ""
                            i += j
                            break
        
        # Add remaining text as the last part
        if current_part:
            parts.append(f'"{current_part.strip()}"')
        
        # If we didn't get enough parts, split the last part
        while len(parts) < num_parts:
            last_part = parts[-1].strip('"')
            split_point = len(last_part) // 2
            parts[-1] = f'"{last_part[:split_point]}"'
            parts.append(f'"{last_part[split_point:]}"')
        
        return parts


if __name__ == "__main__":
    translator = Translator()
    
    # Get all .csv files in the WORK_FOLDER
    csv_files = [f for f in os.listdir(WORK_FOLDER) if f.endswith('.csv')]
    if csv_files:
        for csv_file in csv_files:
            csv_path = os.path.join(WORK_FOLDER, csv_file)
            print(f"\nProcessing: {csv_file}")
            translator.translate_csv(csv_path)
    else:
        print("No CSV files found in the work folder")
